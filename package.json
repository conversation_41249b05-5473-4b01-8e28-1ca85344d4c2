{"private": true, "name": "my-app", "version": "1.0.0", "description": "npm init template for bootstrapping an empty Colyseus project", "main": "build/index.js", "engines": {"node": ">= 16.13.0"}, "scripts": {"start": "tsx watch src/index.ts", "loadtest": "tsx loadtest/example.ts --room my_room --numClients 2", "build": "npm run clean && tsc", "clean": "<PERSON><PERSON><PERSON> build", "test": "mocha -r tsx test/**_test.ts --exit --timeout 15000"}, "author": "", "license": "UNLICENSED", "bugs": {"url": "https://github.com/colyseus/create-colyseus/issues"}, "homepage": "https://github.com/colyseus/create-colyseus#readme", "devDependencies": {"@colyseus/loadtest": "^0.15.0", "@colyseus/testing": "^0.15.0", "@types/express": "^4.17.1", "@types/mocha": "^10.0.1", "mocha": "^10.2.0", "rimraf": "^5.0.0", "tsx": "^4.10.2", "typescript": "^5.0.4"}, "dependencies": {"@colyseus/auth": "^0.15.10", "@colyseus/monitor": "^0.15.7", "@colyseus/playground": "^0.15.3", "@colyseus/tools": "^0.15.0", "brackets-manager": "^1.6.4", "brackets-memory-db": "^1.0.5", "colyseus": "^0.15.0", "express": "^4.18.2", "express-basic-auth": "^1.2.1"}}