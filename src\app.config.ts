import config from "@colyseus/tools";
import { monitor } from "@colyseus/monitor";
import { playground } from "@colyseus/playground";
import { LobbyRoom } from "colyseus";
import express from 'express';
import auth from 'express-basic-auth';
/**
 * Import your Room files
 */
const app = express();
const logs: string[] = [];

export function addLog(log: any): void {
    try {
        logs.push(log);
        console.log('Log added successfully:', log);
    } catch (error) {
        console.error('Error adding log:', error);
    }
}

import { MyRoom } from "./rooms/MyRoom";
import { TournamentRoom } from "./rooms/TournamentRoom";

export default config({
    options: {
        gracefullyShutdown: false,
    },

    initializeGameServer: (gameServer) => {
        /**
         * Define your room handlers:
         */
        gameServer.define('free_battle', MyRoom)
            .filterBy(['private'])
        gameServer.define('ranked_battle', MyRoom);
        gameServer.define('prized_battle', MyRoom);
        gameServer.define('tournament_match', MyRoom);
        gameServer.define("lobby", LobbyRoom);
        gameServer.define("tournament", TournamentRoom);
        console.log(`Game server initialized ${process.env.NODE_ENV} ${process.env.RUN_NUMBER}`);
    },

    initializeExpress: (app) => {
        /**
         * Bind your custom express routes here:
         * Read more: https://expressjs.com/en/starter/basic-routing.html
         */
        app.get("/hello_world", (req, res) => {
            res.send(`remonster! ${process.env.NODE_ENV} ${process.env.RUN_NUMBER}`);
        });

        /**
         * Use @colyseus/playground
         * (It is not recommended to expose this route in a production environment)
         */
        if (process.env.NODE_ENV !== "mainnet") {
            app.use("/playground", playground);
        }

        /**
         * Use @colyseus/monitor
         * It is recommended to protect this route with a password
         * Read more: https://docs.colyseus.io/tools/monitor/#restrict-access-to-the-panel-using-a-password
         */

        const basicAuthMiddleware = auth({
            users: {
                'admin':'j1ku@jd_hf@IU12-3ASD@6db'
            },
            // sends WWW-Authenticate header, which will prompt the user to fill
            // credentials in
            challenge: true
        });

        app.use("/colyseus", basicAuthMiddleware, monitor({
            columns: [
                'roomId',
                'name',
                'clients',
                { metadata: "type" }, // display 'spectators' from metadata
                { metadata: "Owner" } // display 'spectators' from metadata
            ]
        }));
        app.use((req, res, next) => {
            // const log = `${new Date().toISOString()} - ${req.method} ${req.url}`;
            // logs.push(log);
            // console.log(log);
            next();
        });
        app.get('/logs', (req, res) => {
            res.json(logs);
        });
        app.use(express.static('public'));
    },


    beforeListen: () => {
        /**
         * Before before gameServer.listen() is called.
         */
    }
});
