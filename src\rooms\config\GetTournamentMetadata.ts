import * as https from "https";

export interface SponsorTournament {
  _id: string;
  wallet_address: string;
  banner: string;
  tournament_name: string;
  sponsor_name: string;
  date: number;
  end_time: number;
  type: number;
  participants: number;
  jump_in: boolean;
  use_allow_list: boolean;
  allow_list: string[];
  register_list: string[];
  random_list: string[];
  entry_list: string[];
  allow_list_only: boolean;
  monster_rank: number;
  monsters: number;
  turns: number;
  special_hex: number;
  memory: boolean;
  sms: boolean;
  player_rank: number;
  cycle_score_specified: boolean;
  cycle_score_min: number;
  battles_specified: boolean;
  battles_min: number;
  playing_days_specified: boolean;
  playing_days_min: number;
  cycle: any[];
  monster_type: number;
  age_min: number;
  age_max: number;
  terrain_ban: number[];
  main_seed_ban: number[];
  sub_seed_ban: number[];
  is_draft: boolean;
  del_if: number;
  is_deposit_oas: boolean;
  has_reward: boolean;
  state: number;
  date_string: string;
  prize: any[];
  txid: string;
  blockchain_id: string;
  match_list: MatchList[];
  final_standings: FinalStandings[];
}
interface MatchList {
  match_id: number;
  round_id: number;
  player_1: string;
  player_1_id: number;
  player_2: string;
  player_2_id: number;
  state: number;
  room_id: string;
  winner: number;
}
interface FinalStandings {
  id: number;
  name: string;
  rank: number;
  score: number;
  claimed: boolean;
}
interface SponsorTournamentsData {
  sponsor_tournament_list: SponsorTournament[];
}
interface SponsorTournamentData {
  sponsor_tournament: SponsorTournament;
}
interface SponsorTournamentsResponse {
  success: boolean;
  message: string;
  data: SponsorTournamentsData;
  code: number;
}
interface SponsorTournamentsUpdateResponse {
  success: boolean;
  message: string;
  data: SponsorTournament;
  code: number;
}

class SponsorTournamentData implements SponsorTournamentData {
  sponsor_tournament: SponsorTournament;
}

export class FinalStandingsData implements FinalStandings {
  id: number;
  name: string;
  rank: number;
  score: number;
  claimed: boolean;
  constructor(id: number, name: string, rank: number, score: number) {
    this.id = id;
    this.name = name;
    this.rank = rank;
    this.score = score;
  }
}

export class MatchListData implements MatchList {
  match_id: number;
  round_id: number;
  player_1: string;
  player_1_id: number;
  player_2: string;
  player_2_id: number;
  state: number;
  room_id: string;
  winner: number;
  constructor(
    match_id: number,
    round_id: number,
    player_1: string,
    player_1_id: number,
    player_2: string,
    player_2_id: number,
    state: number,
    room_id: string,
    winner: number
  ) {
    this.match_id = match_id;
    this.round_id = round_id;
    this.player_1 = player_1;
    this.player_1_id = player_1_id;
    this.player_2 = player_2;
    this.player_2_id = player_2_id;
    this.state = state;
    this.room_id = room_id;
    this.winner = winner;
  }
}

export const GetTournament = async (
  token: string
): Promise<SponsorTournamentsResponse> => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: process.env.API_URL, // Replace with actual API
      port: 443,
      path: "/api/game/sponsored/tournament/get", // Replace with actual endpoint
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`, // Add Bearer token
        "Content-Type": "application/json",
      },
    };

    const req = https.request(options, (res) => {
      let data = "";

      // Collect response chunks
      res.on("data", (chunk) => {
        data += chunk;
      });

      // End of response
      res.on("end", () => {
        try {
          const parsedData = JSON.parse(data);
          resolve(parsedData);
        } catch (error) {
          reject({ success: false, error: "Error parsing response" });
        }
      });
    });

    // Handle request error
    req.on("error", (e) => {
      reject({ success: false, error: e.message });
    });

    // End the request
    req.end();
  });
};

export const UpdateTournament = async (
  tournament: SponsorTournament
): Promise<SponsorTournamentsUpdateResponse> => {
  return new Promise((resolve, reject) => {
    let sponsorData = new SponsorTournamentData();
    sponsorData.sponsor_tournament = tournament;

    const data = JSON.stringify(sponsorData); // Convert tournament object to JSON
    const options = {
      hostname: process.env.API_URL, // Replace with actual API
      port: 443,
      path: "/api/game/sponsored/tournament/update-private", // Replace with actual endpoint
      method: "POST",
      headers: {
        "Content-Type": "application/json", // Send JSON data
        "Content-Length": data.length, // Specify content length
      },
    };

    const req = https.request(options, (res) => {
      let responseData = "";

      // Collect response chunks
      res.on("data", (chunk) => {
        responseData += chunk;
      });

      // End of response
      res.on("end", () => {
        const parsedData = JSON.parse(responseData);

        if (
          (res.statusCode === 200 || res.statusCode === 201) &&
          parsedData.success === true
        ) {
          resolve(parsedData); // Resolve the promise on success
        } else {
          reject(
            new Error(
              `Failed with status code: ${res.statusCode}. Response: ${responseData}`
            )
          );
        }
      });
    });

    // Handle request error
    req.on("error", (e) => {
      reject(new Error(`Error sending request: ${e.message}`));
    });
    req.write(data);

    // End the request
    req.end();
  });
};
