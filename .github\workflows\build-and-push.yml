name: Build and Push to ACR
on:
  push:
    branches:
      - 'main'
      - 'staging'
      - 'test'
jobs:
  build:
    name: 'Build and Push to ACR'
    runs-on: ubuntu-latest

    defaults:
      run:
        shell: bash

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: <PERSON><PERSON> Login
        uses: azure/docker-login@v1
        with:
          login-server: ${{ secrets.AZURE_URL }}
          username: ${{ secrets.ACR_USERNAME }}
          password: ${{ secrets.ACR_PASSWORD }}

      - name: Build and Push to ACR production
        if: github.ref == 'refs/heads/main'
        uses: docker/build-push-action@v2
        with:
          push: true
          tags: ${{ secrets.AZURE_URL }}/remonster_lobby_server:latest
          build-args: ENVIRONMENT=mainnet
          file: Dockerfile

      - name: Build and Push to ACR staging
        if: github.ref == 'refs/heads/staging'
        uses: docker/build-push-action@v2
        with:
          push: true
          tags: ${{ secrets.AZURE_URL }}/remonster_lobby_server:staging
          build-args: ENVIRONMENT=staging
          file: Dockerfile

      - name: Build and Push to ACR test
        if: github.ref == 'refs/heads/test'
        uses: docker/build-push-action@v2
        with:
          push: true
          tags: ${{ secrets.AZURE_URL }}/remonster_lobby_server:test
          build-args: ENVIRONMENT=development
          file: Dockerfile
