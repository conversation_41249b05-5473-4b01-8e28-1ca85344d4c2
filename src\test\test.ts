import { Brackets<PERSON>anager, helpers } from "brackets-manager";
import { InMemoryDatabase } from "brackets-memory-db";

const TOURNAMENT_ID = 0;

function getNearestPowerOfTwo(input: number): number {
  return Math.pow(2, Math.ceil(Math.log2(input)));
}
export async function process(dataset: Dataset) {
    console.log("/////////////////////////////////");
  const db = new InMemoryDatabase();
  const manager = new BracketsManager(db);

  db.setData({
    participant: [],
    stage: [],
    group: [],
    round: [],
    match: [],
    match_game: [],
  });

  await manager.create.stage({
    name: dataset.title,
    tournamentId: TOURNAMENT_ID,
    type: dataset.type,
    seeding: dataset.user.map((player) => player),
    settings: {
      roundRobinMode: "simple",
      groupCount: 1,
      size: getNearestPowerOfTwo(dataset.user.length),
    },
  });

  const data = await manager.get.stageData(0);
  console.log(JSON.stringify(data.match));
  console.log("/////////////////////////////////");
  await updateAllMatchesWithRandomResults();
  async function updateAllMatchesWithRandomResults() {
    for (let match of data.match) {
      
        // Generate random result for opponent1
        const randomResult = Math.random() > 0.5 ? "win" : "loss";

        // Set the result for opponent2 based on opponent1's result
        const opponent2Result = randomResult === "win" ? "loss" : "win";

        // Update the match using the manager.update.match function
        await manager.update.match({
          id: match.id,
          opponent1: { id: match.opponent1.id, result: randomResult },
          opponent2: { id: match.opponent2.id, result: opponent2Result },
        });
      
    }
  }

  const listMatch: TournamentMatch[] = [];
  data.match.forEach((matchManager) => {
    const player1 =
      matchManager.opponent1 === null || matchManager.opponent1.id === null
        ? ""
        : dataset.user[matchManager.opponent1.id as number];
    const player1id =
      matchManager.opponent1 === null || matchManager.opponent1.id === null
        ? -1
        : (matchManager.opponent1.id as number);
    const player2 =
      matchManager.opponent2 === null || matchManager.opponent2.id === null
        ? ""
        : dataset.user[matchManager.opponent2.id as number];
    const player2id =
      matchManager.opponent2 === null || matchManager.opponent2.id === null
        ? -1
        : (matchManager.opponent2.id as number);
    const winner =
      matchManager.opponent1.result === "win"
        ? (matchManager.opponent1.id as number)
        : (matchManager.opponent2.id as number);
    listMatch.push(
      new TournamentMatch(
        player1,
        player1id,
        player2,
        player2id,
        matchManager.status as number,
        matchManager.id as number,
        matchManager.round_id as number
      )
    );
  });

  console.log(JSON.stringify(listMatch));
  console.log("------------------------------------------");

  const match = (await manager.get.stageData(0)).match.every(
    (m) => m.status >= 4 || m.status === 0
  );
  if (match) {
    console.log(match);
    const final = await getFinalStanding(manager);
    console.log(JSON.stringify(final));
  }

  // const final = await manager.get.finalStandings(0);
  // console.log(final);

  return {
    stages: data.stage,
    matches: data.match,
    matchGames: data.match_game,
    participants: data.participant,
  };
}

async function getFinalStanding(
  manager: BracketsManager
): Promise<FinalStandingsData[]> {
  const data = await manager.get.stageData(0);

  const finalStandings: FinalStandingsData[] = [];
  if (data.stage[0].type === "round_robin") {
    data.participant.forEach((p) =>
      finalStandings.push(new FinalStandingsData(p.id as number, p.name, 0, 0))
    );

    data.match
      .filter((match) => match.status >= 4)
      .forEach((match) => {
        if (match.opponent1.result === "win") {
          finalStandings.find((player) => player.id === match.opponent1.id)
            .score++;
        }
        if (match.opponent2.result === "win") {
          finalStandings.find((player) => player.id === match.opponent2.id)
            .score++;
        }
      });

    const uniqueScores = [
      ...new Set(finalStandings.map((player) => player.score)),
    ].sort((a, b) => b - a);
    finalStandings.forEach((player) => {
      player.rank = uniqueScores.indexOf(player.score) + 1;
    });
  } else {
    var finalStandingData = await manager.get.finalStandings(0);
    finalStandingData.forEach((player) =>
      finalStandings.push(
        new FinalStandingsData(
          player.id as number,
          player.name,
          player.rank,
          -1
        )
      )
    );
  }
  return finalStandings;
}

export class FinalStandingsData {
  id: number;
  name: string;
  rank: number;
  score: number;
  constructor(id: number, name: string, rank: number, score: number) {
    this.id = id;
    this.name = name;
    this.rank = rank;
    this.score = score;
  }
}

export const dataset8: Dataset = {
  title: "tournament",
  type: "single_elimination",
  user: [
    "0x75320123A89D78AC8B77d3bD357Be97380b38DDD",
    "0x24a00DfA3030A6D91C263285388FfCF5daf367f2",
    "0xa4B56b5D59d04f859b0ef4873F985906675d3fAd",
    "0x926A80dEfCfb7130E02E1BE68fF52354E865d6c8",
    "0x8D397a24215D1ce2dEa1f5bF1bf3739BDd421686",
    "0x1794Cd0EB4E6c9821aC7bdaEBb49df5E362B9F6F",
    "0x531cE821Cf30f26730c0Ac122C20ADE5f41CA830",
    "0x7D299F9EA013eD6E77B54B1BcA14B705d825E1fA",
    "0x3C971ccf2F799EBa65EA25E7461D7Ad438c811aD",
    "0xbf8C733F02239BaA3278cbFda3da7814B788E9e9",
    "0xa06e2A7ad27Ef837Ef7C7B1371Fe3965166B931f",
    "0x3E0a866D29628BE1f965569839C22b7fBF238556",
    "0xD2671d09550F427b7C224aE3e7a3669e78abB58a",
    "0x050Ca0c5C3e446c88c729404Be6205A2eC28E04b",
    "0x94240D8952CB36Aa5eFD67047c4eb71E57414BBB",
    "0x7A5322B9C4Ce55067E146667aD943F7f6c930C8F",
  ],
};

declare global {
  interface Window {
    bracketsViewer?: any | undefined;
  }

  interface Dataset {
    title: string;
    type: StageType;
    user: string[];
  }
}
import { StageType } from "brackets-model";
import { TournamentMatch } from "../rooms/schema/TournamentState";
