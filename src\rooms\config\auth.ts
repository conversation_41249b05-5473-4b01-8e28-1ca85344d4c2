import * as https from "https";

export interface ApiResponse {
  success: boolean;
  message: string;
  data:    Data;
  code:    number;
}

export interface Data {
  player_info: PlayerInfo;
}

export interface PlayerInfo {
  _id:             string;
  player_rank:     number;
  player_bio:      string;
  player_language: string;
  user_id:         string;
  user_name:       string;
  address_wallet:  string;
  use_yn:          string;
  bit_balance:     number;
  del_if:          number;
  phone_verified:  boolean;
  email_verified:  boolean;
  crt_dt:          Date;
  ipaddress:       string;
  last_active:     Date;
  role:            string;
  player_time:     number;
  last_login:      Date;
  timezone_offset: number;
  total_login_day: number;
  login_day:       number;
  mod_dt:          Date;
  mapoint_amount:  number;
  player_phone:    null;
  email:           null;
  player_twitter:  null;
  avatar:          string;
  user_pw:         string;
}

export const GetUserInfo = async (token: string): Promise<ApiResponse> => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: process.env.API_URL, // Replace with actual API
      port: 443,
      path: "/api/user/playerinfo", // Replace with actual endpoint
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`, // Add Bearer token
        "Content-Type": "application/json",
      },
    };

    const req = https.request(options, (res) => {
      let data = "";

      // Collect response chunks
      res.on("data", (chunk) => {
        data += chunk;
      });

      // End of response
      res.on("end", () => {
        try {
          const parsedData = JSON.parse(data);
          resolve(parsedData);
        } catch (error) {
          reject({ success: false, error: "Error parsing response" });
        }
      });
    });

    // Handle request error
    req.on("error", (e) => {
      reject({ success: false, error: e.message });
    });

    // End the request
    req.end();
  });
};
