import { Buffs, BuffsEnum, Debuffs, DebuffsEnum } from "./schema/Monster";

export function SyncTurnData(state: any, message: any) {
  SyncTurn(state, message);
  SyncMonsterData(state, message);
}

function SyncTurn(state: any, message: any) {
  state.turn = message.turnCount;
  state.currentSelectedMonster = message.currentSelectedMonster;
  state.currentMonsterTurn = [];
  message.currentMonsterTurn.forEach((element: any) => {
    state.currentMonsterTurn.push(element);
  });
  state.nextMonsterTurn = [];
  message.nextMonsterTurn.forEach((element: any) => {
    state.nextMonsterTurn.push(element);
  });
}
function SyncMonsterData(state: any, message: any) {
  message.monsterData.forEach((element: any) => {
    state.monsters.forEach((monster: any) => {
      if (monster.monsterId === element.monsterId) {
        monster.isDead = element.isDead;
        monster.position.x = element.position.x;
        monster.position.y = element.position.y;
        // update basicParameter for health, strength, intelligent, dexterity, agility, vitality
        monster.basicParameter.health = element.basicParameter.health;
        monster.basicParameter.strength = element.basicParameter.strength;
        monster.basicParameter.intelligent = element.basicParameter.intelligent;
        monster.basicParameter.dexterity = element.basicParameter.dexterity;
        monster.basicParameter.agility = element.basicParameter.agility;
        monster.basicParameter.vitality = element.basicParameter.vitality;
        // udate currentSpeed,currentStaminaRecover,currentMoveLimit
        monster.currentSpeed = element.currentSpeed;
        monster.currentStaminaRecover = element.currentStaminaRecover;
        monster.currentMoveLimit = element.currentMoveLimit;
        monster.currentHealthPercent = element.currentHealthPercent;
        // clear buffs and debuffs
        monster.buffs = [];
        monster.debuffs = [];
        // update buffs and debuffs, loop through the array and add to the monster
        Object.entries(
          element.buffs as Record<string, { value: number; duration: number }>
        ).forEach(
          ([buffName, buff]: [string, { value: number; duration: number }]) => {
            const newBuff = new Buffs();
            newBuff.buffEnum = buffName as BuffsEnum;
            newBuff.value = buff.value;
            newBuff.duration = buff.duration;
            monster.buffs.push(newBuff);
          }
        );
        // DO THE same for debuffs
        Object.entries(
          element.debuffs as Record<string, { value: number; duration: number }>
        ).forEach(
          ([debuffName, debuff]: [
            string,
            { value: number; duration: number }
          ]) => {
            const newDebuff = new Debuffs();
            newDebuff.debuffEnum = debuffName as DebuffsEnum;
            newDebuff.value = debuff.value;
            newDebuff.duration = debuff.duration;
            monster.debuffs.push(newDebuff);
          }
        );
      }
    });
  });
}
