import { Schema, type, ArraySchema, MapSchema } from "@colyseus/schema";
import { string } from "@colyseus/schema/lib/encoding/decode";
import { SponsorTournament } from "../config/GetTournamentMetadata";

// export enum TournamentStateEnum {
//   Upcoming = 0,
//   Registration = 1,
//   Entry = 2,
//   JumpIn = 3,
//   InProgress = 4,
//   Ended = 5
// }

// /** The two matches leading to this one are not completed yet. */
// Locked = 0,
// /** One participant is ready and waiting for the other one. */
// Waiting = 1,
// /** Both participants are ready to start. */
// Ready = 2,
// /** The match is running. */
// Running = 3,
// /** The match is completed. */
// Completed = 4,
// /** At least one participant completed his following match. */
// Archived = 5

export class TournamentMatch extends Schema {
  @type("int32") match_id: number = 0;
  @type("int32") round_id: number = 0;
  @type("string") player_1: string = "";
  @type("int32") player_1_id: number = -1;
  @type("string") player_2: string = "";
  @type("int32") player_2_id: number = -1;
  @type("int32") state: number = 0;
  @type("string") room_id: string = "";
  @type("int32") winner: number = -1;
  @type("boolean") player_1_forfeit: boolean = false;
  @type("boolean") player_2_forfeit: boolean = false;

  constructor(
    player_1: string = "",
    player_1_id: number = -1,
    player_2: string = "",
    player_2_id: number = -1,
    state: number = 0,
    match_id: number = 0,
    round_id: number = 0,
    room_id: string = "",
    winner: number = -1
  ) {
    super();
    this.player_1 = player_1 || ""; // Ensure empty string if null/undefined
    this.player_1_id = player_1_id ?? -1; // Ensure -1 if null/undefined
    this.player_2 = player_2 || ""; // Ensure empty string if null/undefined
    this.player_2_id = player_2_id ?? -1; // Ensure -1 if null/undefined
    this.state = state ?? 0; // Ensure 0 if null/undefined
    this.match_id = match_id ?? 0; // Ensure 0 if null/undefined
    this.round_id = round_id ?? 0; // Ensure 0 if null/undefined
    this.room_id = room_id || ""; // Ensure empty string if null/undefined
    this.winner = winner ?? -1; // Ensure -1 if null/undefined
  }
}

export class TournamentMetaData extends Schema {
  @type("string") tournament_name: string;
  @type("string") sponsor_name: string;
  @type("int32") date: number;
  @type("boolean") jump_in: boolean;
  @type("boolean") use_allow_list: boolean;
  @type("boolean") allow_list_only: boolean;
  @type("int32") participants: number;
  @type("int32") type: number;
  @type(["string"]) allow_list = new ArraySchema<string>();
  @type(["string"]) register_list = new ArraySchema<string>();
  @type(["string"]) random_list = new ArraySchema<string>();

  constructor(
    tournament_name: string,
    sponsor_name: string,
    date: number,
    jump_in: boolean,
    use_allow_list: boolean,
    allow_list_only: boolean,
    participants: number,
    type: number,
    allow_list: string[] = [],
    register_list: string[] = [],
    random_list: string[] = []
  ) {
    super();
    this.tournament_name = tournament_name;
    this.sponsor_name = sponsor_name;
    this.date = date;
    this.jump_in = jump_in;
    this.use_allow_list = use_allow_list;
    this.allow_list_only = allow_list_only;
    this.participants = participants;
    this.type = type;
    this.allow_list = new ArraySchema(...allow_list);
    this.register_list = new ArraySchema(...register_list);
    this.random_list = new ArraySchema(...random_list);
  }
}

export class FinalStandingsState extends Schema {
  @type("int32") id: number;
  @type("string") name: string;
  @type("int32") rank: number;
  @type("int32") score: number;

  constructor(id: number, name: string, rank: number, score: number) {
    super();
    this.id = id;
    this.name = name;
    this.rank = rank;
    this.score = score;
  }
}

export class TournamentState extends Schema {
  @type("string") _id: string = "";
  @type("int32") state: number = 1;
  @type("int32") round: number = 0;
  @type(["string"]) entry_list = new ArraySchema<string>();
  @type(TournamentMetaData) metadata: TournamentMetaData;
  @type([TournamentMatch]) match_list = new ArraySchema<TournamentMatch>();
  @type([FinalStandingsState]) final_standings = new ArraySchema<FinalStandingsState>();
  @type({ map: "string" }) sessions = new MapSchema<string, string>();
  tournament: SponsorTournament;
}

export class ClientData {
  _id: string;
  player_address: string;
  token: string;
  watch: boolean;
}
