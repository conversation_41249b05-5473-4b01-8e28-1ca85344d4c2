import { Schema, type, ArraySchema } from "@colyseus/schema";

export class Vector2Cus extends Schema {
    @type("number")
    x: number;

    @type("number")
    y: number;

    constructor(x: number = 100, y: number = 100) {
        super();
        this.x = x;
        this.y = y;
    }
}

export class BasicParameter extends Schema {
    @type("float32") health: number;
    // do the same for strengt, intelligent, dexterity, agility and vitality
    @type("float32") strength: number;
    @type("float32") intelligent: number;
    @type("float32") dexterity: number;
    @type("float32") agility: number;
    @type("float32") vitality: number;
}

export enum BuffsEnum{
    Veil = "Veil",
    Barrier = "Barrier",
    Activation = "Activation",
    Acceleration = "Acceleration",
    Sink = "Sink",
    Illusion = "Illusion",
    Provocation = "Provocation",
    Invisibility = "Invisibility",
    StrIncrease = "StrIncrease",
    IntIncrease = "IntIncrease",
    DexIncrease = "DexIncrease",
    AgiIncrease = "AgiIncrease",
    VitIncrease = "VitIncrease",
    Courage = "Courage",
    Cheering = "Cheering",
    Concentration = "Concentration",
    Energize = "Energize"
}

export enum DebuffsEnum{
    Poison = "Poison",
    Toxic = "Toxic",
    Weakend = "Weakend",
    Blind = "Blind",
    Slow = "Slow",
    Paralyze = "Paralyze",
    Sleep = "Sleep",
    StrDecrease = "StrDecrease",
    IntDecrease = "IntDecrease",
    DexDecrease = "DexDecrease",
    AgiDecrease = "AgiDecrease",
    VitDecrease = "VitDecrease",
    Enrage = "Enrage",
    Fear = "Fear",
    Confusion = "Confusion",
    Charm = "Charm"
}

export class Buffs extends Schema {
    @type("string") buffEnum: BuffsEnum;
    @type("float32") value: number;
    @type("float32") duration: number;
}
export class Debuffs extends Schema {
    @type("string") debuffEnum: DebuffsEnum;
    @type("float32") value: number;
    @type("float32") duration: number;
}

export class Monster extends Schema {
    @type("string") ownerId: string = "";
    @type("string") monsterData: string = "";
    @type("string") monsterId: string = "";
    @type("float32") randomNumberCorrection: number
    @type("float32") currentSpeed: number;
    @type("float32") currentStaminaRecover: number;
    @type("float32") currentMoveLimit: number;
    @type("float32") currentHealthPercent: number;
    @type("boolean") isDead: boolean = false;
    @type(Vector2Cus) position: Vector2Cus = new Vector2Cus();
    @type(BasicParameter) basicParameter: BasicParameter = new BasicParameter();
    @type([Buffs]) buffs: ArraySchema<Buffs> = new ArraySchema<Buffs>();
    @type([Debuffs]) debuffs: ArraySchema<Debuffs> = new ArraySchema<Debuffs>();
}
