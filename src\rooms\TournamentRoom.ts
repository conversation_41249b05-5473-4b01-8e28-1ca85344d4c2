import { Room, ServerError, Client } from "colyseus";
import { GetUserInfo } from "./config/auth";
import {
  GetTournament,
  UpdateTournament,
  SponsorTournament,
  MatchListData,
  FinalStandingsData,
} from "./config/GetTournamentMetadata";
import { shuffle } from "./config/shuffle";
import {
  TournamentState,
  ClientData,
  TournamentMetaData,
  TournamentMatch,
  FinalStandingsState,
} from "./schema/TournamentState";
import { logger } from "@colyseus/core";
import { BracketsManager } from "brackets-manager";
import { InMemoryDatabase } from "brackets-memory-db";
import { matchMaker } from "colyseus";
import { Status } from "brackets-model";

export class TournamentRoom extends Room<TournamentState> {
  private manager: BracketsManager;
  private matchUpdateQueue: Promise<void> = Promise.resolve();

  // Helper method to queue match updates to prevent race conditions
  private queueMatchUpdate<T>(updateFunction: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.matchUpdateQueue = this.matchUpdateQueue
        .then(async () => {
          try {
            const result = await updateFunction();
            resolve(result);
          } catch (error) {
            reject(error);
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  static async onAuth(token: string, _: import("http").IncomingMessage) {
    const authResponse = await GetUserInfo(token);
    if (!authResponse.success) {
      throw new ServerError(400, "Unauthorized");
    }
  }

  async onCreate(option: ClientData) {
    const authResponse = await GetUserInfo(option.token);
    if (
      !authResponse.success ||
      authResponse.data.player_info.address_wallet != option.player_address
    ) {
      // throw new ServerError(400,`cannot verify player ${authResponse.message}`);
      logger.error(`cannot verify player ${authResponse.message}`);
    }

    const storage = new InMemoryDatabase();
    this.manager = new BracketsManager(storage);

    let tournament: SponsorTournament;
    try {
      const tournamentResponse = await GetTournament(option.token);
      if (!tournamentResponse.success) {
        // throw new ServerError(404, "Tournament not found");
        logger.error("Tournament not found");
      } else {
        tournament = getSponsorTournamentById(
          tournamentResponse.data.sponsor_tournament_list,
          option._id
        );
      }
      // Further processing of tournament data
    } catch (error) {
      logger.error("Failed to process tournament data:", error);
      // throw new ServerError(500, "Failed to process tournament data");
    }

    this.roomId = option._id;
    this.setState(new TournamentState());
    tournament.state = this.state.state = 2;
    this.state._id = option._id;

    this.state.metadata = new TournamentMetaData(
      tournament.tournament_name,
      tournament.sponsor_name,
      tournament.date,
      tournament.jump_in,
      tournament.use_allow_list,
      tournament.allow_list_only,
      tournament.participants,
      tournament.type,
      tournament.allow_list,
      tournament.register_list,
      []
    );

    //Check random list
    tournament.random_list = [];
    tournament.register_list.forEach((player) => {
      tournament.random_list.push(player);
    });

    if (tournament.register_list.length > tournament.participants) {
      shuffle(tournament.random_list);
      for (let i = 0; i < tournament.register_list.length; i++) {
        if (i >= tournament.participants) {
          tournament.random_list.pop();
        }
      }
    }
    tournament.random_list.forEach((player) => {
      this.state.metadata.random_list.push(player);
    });
    //Set tournament state enum
    const now = Date.now();
    const tournamentTime = this.state.metadata.date * 1000; // seconds to ms
    const inProgressDiff = tournamentTime - now;
    this.clock.start();

    if (this.state.metadata.jump_in) {
      const fiveMinutesInMs = 10 * 60 * 1000; // 10 minutes in milliseconds
      const jumpInDiff = tournamentTime - fiveMinutesInMs - now;

      if (jumpInDiff > 0 && inProgressDiff > 0) {
        this.clock.setTimeout(() => {
          if (
            this.state.state < 3 &&
            this.state.metadata.jump_in &&
            this.state.entry_list.length < this.state.metadata.participants
          )
            // JumpIn = 3
            this.state.state = 3;
          tournament.state = 3;
          updateTournamentAsync(tournament);
          this.state.tournament = tournament;
        }, jumpInDiff);
      } else {
        tournament.state = this.state.state = 3;
        updateTournamentAsync(tournament);
        this.state.tournament = tournament;
      }
    }

    if (inProgressDiff > 0) {
      this.clock.setTimeout(async () => {
        if (this.state.state < 4 && this.state.entry_list.length >= 2) {
          this.state.state = 4; // InProgress = 4
          try {
            await tournamentCreate(this.manager, storage, this.state, tournament);
          } catch (error) {
            tournament.state = 5;
            this.state.state = 5;
            updateTournamentAsync(tournament);
            this.state.tournament = tournament;
            logger.error("Cannot create tournament:", error);
            this.clients.forEach((client) => {
              client.error(400, `Cannot create tournament ${error}`);
            });
            this.disconnect();
            // throw new ServerError(400, "Cannot create tournament");
          }
        } else {
          tournament.state = 5;
          this.state.state = 5;
          updateTournamentAsync(tournament);
          this.state.tournament = tournament;
          logger.error("Cannot create tournament");
          this.clients.forEach((client) => {
            client.error(400, "Cannot create tournament");
          });
          this.disconnect();
          // throw new ServerError(400, "Cannot create tournament");
        }
      }, inProgressDiff);
    } else {
      logger.error(
        `wrong tournament time: ${this.state._id}, ${this.state.metadata.tournament_name}, ${tournamentTime} ${now}`
      );
      this.clients.forEach((client) => {
        client.error(400, "Tournament ended");
      });
      this.disconnect();
      // throw new ServerError(400, "Tournament ended");
    }

    this.onMessage(
      "match_forfeit",
      (client: Client, message: TournamentMatch) => {
        this.queueMatchUpdate(() =>
          updateTournamentMatchForfeit(
            client,
            this.manager,
            this.state,
            tournament,
            message.match_id,
            message.player_1_id,
            message.player_2_id,
            message.player_1_forfeit,
            message.player_2_forfeit
          )
        );
      }
    );

    this.onMessage(
      "match_started",
      (client: Client, message: TournamentMatch) => {
        this.queueMatchUpdate(() =>
          updateTournamentMatchProgress(
            client,
            this.manager,
            this.state,
            tournament,
            message.match_id,
            message.player_1_id,
            message.player_2_id,
            message.room_id
          )
        );
      }
    );

    this.onMessage(
      "match_finished",
      (client: Client, message: TournamentMatch) => {
        this.queueMatchUpdate(() =>
          updateTournamentMatchResult(
            client,
            this.manager,
            this.state,
            tournament,
            message.match_id,
            message.player_1_id,
            message.player_2_id,
            message.winner
          )
        );
      }
    );
  }
  async onLeave(client: Client, consented: boolean) {
    const playerAddress = this.state.sessions.get(client.sessionId);
    if (playerAddress && this.state.entry_list.includes(playerAddress)) {
      // If tournament is active (state < 5) and player is in an active match, mark as forfeit
      if (this.state.state === 4) {
        // Find any active matches for this player
        const playerMatches = this.state.match_list.filter(match => 
          (match.player_1 === playerAddress || match.player_2 === playerAddress) && 
          match.state < 4 // Match not completed yet
        );
        
        // Mark player as forfeit in all active matches
        for (const match of playerMatches) {
          const isPlayer1 = match.player_1 === playerAddress;
          this.queueMatchUpdate(() =>
            updateTournamentMatchForfeit(
              client,
              this.manager,
              this.state,
              this.state.tournament,
              match.match_id,
              match.player_1_id,
              match.player_2_id,
              isPlayer1, // Set forfeit flag based on which player left
              !isPlayer1
            )
          );
        }
      }
      
      // Remove player from entry list
      this.state.entry_list.splice(
        this.state.entry_list.indexOf(playerAddress),
        1
      );
    }
    
    if (
      this.state.entry_list.length === 0 &&
      this.state.final_standings.length === 0
    ) {
      this.state.state = 5;
      this.state.tournament.state = 5;
      await updateTournamentAsync(this.state.tournament);
    }
    this.state.sessions.delete(client.sessionId);
  }
  
  async onJoin(client: Client, option: ClientData) {
    const response = await GetUserInfo(option.token);
    if (
      !response.success ||
      response.data.player_info.address_wallet != option.player_address
    ) {
      logger.error(`cannot authenticate player ${response.message}`);
      client.error(401, `Cannot authenticate player ${response.message}}`);
      // throw new ServerError(401, `cannot authenticate player ${response.message}}`);
    } else {
      this.state.sessions.set(client.sessionId, option.player_address);
      if (!option.watch) {
        if (
          this.state.state === 2 &&
          this.state.metadata.random_list.includes(option.player_address) &&
          !this.state.entry_list.includes(option.player_address)
        ) {
          this.state.entry_list.push(option.player_address);
        }

        if (
          this.state.state === 3 &&
          this.state.metadata.jump_in === true &&
          this.state.entry_list.length < this.state.metadata.participants &&
          !this.state.entry_list.includes(option.player_address)
        ) {
          this.state.entry_list.push(option.player_address);
        }
      }
    }
  }
}

function getSponsorTournamentById(
  sponsorTournaments: SponsorTournament[],
  id: string
): SponsorTournament {
  return sponsorTournaments.find((tournament) => tournament._id === id);
}

async function getFinalStanding(
  manager: BracketsManager
): Promise<FinalStandingsData[]> {
  try {
    const type = (await manager.get.stageData(0)).stage[0].type;
    const finalStandings: FinalStandingsData[] = [];
    if (type === "round_robin") {
      const data = await manager.get.stageData(0);

      data.participant.forEach((p) =>
        finalStandings.push(
          new FinalStandingsData(p.id as number, p.name, 0, 0)
        )
      );

      data.match
        .filter((match) => match.status >= 4)
        .forEach((match) => {
          if (match.opponent1.result === "win") {
            finalStandings.find((player) => player.id === match.opponent1.id)
              .score++;
          }
          if (match.opponent2.result === "win") {
            finalStandings.find((player) => player.id === match.opponent2.id)
              .score++;
          }
        });

      const uniqueScores = [
        ...new Set(finalStandings.map((player) => player.score)),
      ].sort((a, b) => b - a);

      finalStandings.forEach((player) => {
        player.rank = uniqueScores.indexOf(player.score) + 1;
      });
    } else {
      const finalStandingData = await manager.get.finalStandings(0);
      finalStandingData.forEach((player) =>
        finalStandings.push(
          new FinalStandingsData(
            player.id as number,
            player.name,
            player.rank,
            -1
          )
        )
      );
    }
    return finalStandings;
  } catch (error) {
    logger.error("Error getting final standings", error);
    return [];
  }
}

async function updateTournamentMatchToState(
  manager: BracketsManager,
  tournamentSate: TournamentState,
  sponsorTournament: SponsorTournament
) {
  try {
    const managerData = await manager.get.stageData(0);

    // Always rebuild the match_list to ensure correct player IDs and order
    tournamentSate.match_list.length = 0;
    for (const matchManager of managerData.match) {
      let winner: number = -1; // Skip matches that are not started
      if (matchManager.status >= 4)
        winner =
          matchManager.opponent1?.result === "win" || matchManager.opponent2?.id === -1
            ? (matchManager.opponent1.id as number)
            : matchManager.opponent2?.result === "win"
            ? (matchManager.opponent2.id as number)
            : -1;
      
      tournamentSate.match_list.push(
        new TournamentMatch(
          matchManager.opponent1?.id != null &&
          (matchManager.opponent1.id as number) >= 0
            ? tournamentSate.entry_list[matchManager.opponent1.id as number]
            : "",
          (matchManager.opponent1?.id as number) ?? -1,
          matchManager.opponent2?.id != null &&
          (matchManager.opponent2.id as number) >= 0
            ? tournamentSate.entry_list[matchManager.opponent2.id as number]
            : "",
          (matchManager.opponent2?.id as number) ?? -1,
          matchManager.status as number,
          matchManager.id as number,
          matchManager.round_id as number,
          `${sponsorTournament._id}_${matchManager.id}`,
          winner
        )
      );
    }
    await checkFinalStatus(manager, tournamentSate, sponsorTournament);
  } catch (error) {
    console.error("updateTournamentMatchToState error:", error);
  }
}

async function tournamentCreate(
  manager: BracketsManager,
  storage: InMemoryDatabase,
  tournamentSate: TournamentState,
  sponsorTournament: SponsorTournament
) {
  try {
    storage.setData({
      participant: [],
      stage: [],
      group: [],
      round: [],
      match: [],
      match_game: [],
    });

    tournamentSate.metadata.type =
      tournamentSate.metadata.type === 1 && tournamentSate.entry_list.length > 2
        ? 1
        : 0;

    await manager.create.stage({
      name: tournamentSate.metadata.tournament_name,
      tournamentId: tournamentSate._id,
      type:
        tournamentSate.metadata.type === 1 &&
        tournamentSate.entry_list.length > 2
          ? "round_robin"
          : "single_elimination",
      seeding: tournamentSate.entry_list.map((player) => player),
      settings: {
        roundRobinMode: "simple",
        groupCount: 1,
        size: getNearestPowerOfTwo(tournamentSate.entry_list.length),
      },
    });

    await updateTournamentMatchToState(
      manager,
      tournamentSate,
      sponsorTournament
    );

    sponsorTournament.state = tournamentSate.state;

    sponsorTournament.random_list = [];
    tournamentSate.metadata.random_list.forEach((random) =>
      sponsorTournament.random_list.push(random)
    );

    sponsorTournament.entry_list = [];
    tournamentSate.entry_list.forEach((entry) =>
      sponsorTournament.entry_list.push(entry)
    );

    sponsorTournament.match_list = [];
    tournamentSate.match_list.forEach((match) =>
      sponsorTournament.match_list.push(
        new MatchListData(
          match.match_id,
          match.round_id,
          match.player_1,
          match.player_1_id,
          match.player_2,
          match.player_2_id,
          match.state,
          "",
          match.winner
        )
      )
    );
    tournamentSate.tournament = sponsorTournament;
    await updateTournamentAsync(sponsorTournament);
  } catch (error) {
    logger.error(`tournamentCreate ${error}`);
  }
}

async function updateTournamentMatchProgress(
  client: Client,
  manager: BracketsManager,
  tournamentSate: TournamentState,
  sponsorTournament: SponsorTournament,
  matchId: number,
  player1Id: number,
  player2Id: number,
  roomId: string
) {
  try {
    // Check if match id state = 3 then return
    const match = (await manager.get.stageData(0)).match.find(
      (m) => m.id === matchId && m.status === Status.Running
    );

    if (match) return;
    
    await manager.update.match({
      id: matchId, // First match of winner bracket (round 1)
      status: Status.Running,
      opponent1: { id: player1Id },
      opponent2: { id: player2Id },
    });

    await new Promise((resolve) => setTimeout(resolve, 10));
    
    // Always refresh the match list from the bracket manager to ensure correct state
    await updateTournamentMatchToState(
      manager,
      tournamentSate,
      sponsorTournament
    );
  } catch (error) {
    client.error(406, `UpdateTournamentMatchProgress ${error}`);
    logger.error(`code 406, UpdateTournamentMatchProgress ${error}`);
  }
}
async function updateTournamentMatchForfeit(
  client: Client,
  manager: BracketsManager,
  tournamentSate: TournamentState,
  sponsorTournament: SponsorTournament,
  matchId: number,
  player1Id: number,
  player2Id: number,
  player1Forfeit: boolean,
  player2Forfeit: boolean
) {
  try {
    await manager.update.match({
      id: matchId,
      status: 4,
      opponent1: {
        id: player1Id,
        forfeit: player1Forfeit,
      },
      opponent2: {
        id: player2Id,
        forfeit: player2Forfeit,
      },
    });

    // First update the tournament state to get fresh data
    await updateTournamentMatchToState(
      manager,
      tournamentSate,
      sponsorTournament
    );

    // Now calculate the next round based on fresh data
    const roundList: number[] = [];
    tournamentSate.match_list.forEach((match) => {
      if (match.winner < 0 && match.player_1_id > -1 && match.player_2_id > -1)
        roundList.push(match.round_id);
    });

    // Only proceed if there are unfinished matches
    if (roundList.length > 0) {
      const newRound = Math.min(...roundList);
      await checkNewRound(
        newRound,
        client,
        manager,
        tournamentSate,
        sponsorTournament
      );
    }
  } catch (error) {
    client.error(406, `UpdateTournamentMatchForfeit ${error}`);
    logger.error(`code 406, UpdateTournamentMatchForfeit ${error}`);
  }
}

async function updateTournamentMatchResult(
  client: Client,
  manager: BracketsManager,
  tournamentSate: TournamentState,
  sponsorTournament: SponsorTournament,
  matchId: number,
  player1Id: number,
  player2Id: number,
  winnerId: number
) {
  try {
    await manager.update.match({
      id: matchId,
      status: 4,
      opponent1: {
        id: player1Id,
        result: winnerId === player1Id ? "win" : "loss",
      },
      opponent2: {
        id: player2Id,
        result: winnerId === player2Id ? "win" : "loss",
      },
    });

    // First update the tournament state to get fresh data
    await updateTournamentMatchToState(
      manager,
      tournamentSate,
      sponsorTournament
    );

    // Now calculate the next round based on fresh data
    const roundList: number[] = [];
    tournamentSate.match_list.forEach((match) => {
      if (match.winner < 0 && match.player_1_id > -1 && match.player_2_id > -1)
        roundList.push(match.round_id);
    });

    // Only proceed if there are unfinished matches
    if (roundList.length > 0) {
      const newRound = Math.min(...roundList);
      await checkNewRound(
        newRound,
        client,
        manager,
        tournamentSate,
        sponsorTournament
      );
    }

  } catch (error) {
    client.error(406, `UpdateTournamentMatchResult ${error}`);
    logger.error(`code 406, UpdateTournamentMatchResult ${error}`);
  }
}
async function checkNewRound(
  newRound: number,
  client: Client,
  manager: BracketsManager,
  tournamentSate: TournamentState,
  sponsorTournament: SponsorTournament
) {
  if (newRound > tournamentSate.round) {
    tournamentSate.round = newRound;

    const newRoundMatches = tournamentSate.match_list.filter(
      (match) => match.round_id === newRound
    );

    for (const match of newRoundMatches) {
      if (match.player_1_id > -1 && match.player_2_id > -1) {
        const player1Online = Array.from(
          tournamentSate.sessions.values()
        ).includes(match.player_1);
        const player2Online = Array.from(
          tournamentSate.sessions.values()
        ).includes(match.player_2);

        if (player1Online && !player2Online) {
          await updateTournamentMatchResult(
            client,
            manager,
            tournamentSate,
            sponsorTournament,
            match.match_id,
            match.player_1_id,
            match.player_2_id,
            match.player_1_id
          );
        } else if (player2Online && !player1Online) {
          await updateTournamentMatchResult(
            client,
            manager,
            tournamentSate,
            sponsorTournament,
            match.match_id,
            match.player_1_id,
            match.player_2_id,
            match.player_2_id
          );
        }
      } else {
        await updateTournamentMatchResult(
          client,
          manager,
          tournamentSate,
          sponsorTournament,
          match.match_id,
          match.player_1_id,
          match.player_2_id,
          match.player_1_id
        );
      }
    }
  }
}
async function checkFinalStatus(
  manager: BracketsManager,
  tournamentSate: TournamentState,
  sponsorTournament: SponsorTournament
) {
  try {
    const isAllMatchesComplete = await (
      await manager.get.stageData(0)
    ).match.every((m) => m.status >= 4 || m.status === 0);
    if (isAllMatchesComplete === true) {
      const finalStandings = await getFinalStanding(manager);
      sponsorTournament.state = 5;
      tournamentSate.state = 5;
      sponsorTournament.final_standings = finalStandings;
      
      // Clear existing final standings before adding new ones
      tournamentSate.final_standings.length = 0;
      
      finalStandings.forEach((player) => {
        tournamentSate.final_standings.push(
          new FinalStandingsState(
            player.id as number,
            player.name,
            player.rank,
            player.score
          )
        );
      });
      
      // Rest of the function remains the same
      sponsorTournament.match_list = [];
      tournamentSate.match_list.forEach((match) =>
        sponsorTournament.match_list.push(
          new MatchListData(
            match.match_id,
            match.round_id,
            match.player_1,
            match.player_1_id,
            match.player_2,
            match.player_2_id,
            match.state,
            "",
            match.winner
          )
        )
      );
      sponsorTournament.end_time = Math.round(Date.now() / 1000);
      tournamentSate.tournament = sponsorTournament;
      await updateTournamentAsync(sponsorTournament);
    }
  } catch (error) {
    logger.error(`checkFinalStatus ${error}`);
  }
}
async function updateTournamentAsync(tournament: SponsorTournament) {
  try {
    await UpdateTournament(tournament);
  } catch (error) {
    logger.error(`updateTournament ${error}`);
  }
}
async function createRoom(
  manager: BracketsManager,
  tournamentSate: TournamentState,
  sponsorTournament: SponsorTournament
) {
  (await manager.get.stageData(0)).match.forEach(async (match) => {
    if (match.opponent1 != null && match.opponent2 != null) {
      await matchMaker.createRoom("free_battle", {
        roomName: `${sponsorTournament._id}_${match.id}`,
        password: "",
        private: false,
        metadata: {
          firstPlayer: match.opponent1.id,
          secondPlayer: match.opponent2.id,
          audience: true,
          opponent: true,
          monsterRank: sponsorTournament.monster_rank.toString().toUpperCase(),
          monsterNum: sponsorTournament.monsters.toString(),
          turnNum: sponsorTournament.turns.toString(),
          specialHex: sponsorTournament.special_hex.toString(),
          memoryAllow: sponsorTournament.memory,
          hostId: sponsorTournament._id,
        },
      });
    }
  });
}

function getNearestPowerOfTwo(input: number): number {
  return Math.pow(2, Math.ceil(Math.log2(input)));
}
